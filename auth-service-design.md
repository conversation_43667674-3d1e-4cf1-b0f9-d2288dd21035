# Reusable Tudip Authentication Service

## Architecture Overview

The reusable authentication service will be designed as a standalone microservice that can be integrated into any application requiring Tudip email authentication.

## Core Components

### 1. Authentication Service Core
```
tudip-auth-service/
├── src/
│   ├── auth/
│   │   ├── auth.module.ts
│   │   ├── auth.service.ts
│   │   ├── auth.controller.ts
│   │   ├── strategies/
│   │   │   ├── google-oauth.strategy.ts
│   │   │   └── jwt.strategy.ts
│   │   ├── guards/
│   │   │   ├── auth.guard.ts
│   │   │   └── roles.guard.ts
│   │   ├── decorators/
│   │   │   ├── current-user.decorator.ts
│   │   │   └── roles.decorator.ts
│   │   └── dto/
│   │       ├── login.dto.ts
│   │       ├── token.dto.ts
│   │       └── user.dto.ts
│   ├── users/
│   │   ├── users.module.ts
│   │   ├── users.service.ts
│   │   └── users.repository.ts
│   ├── config/
│   │   ├── auth.config.ts
│   │   └── database.config.ts
│   └── shared/
│       ├── interfaces/
│       ├── types/
│       └── utils/
├── database/
│   ├── migrations/
│   └── schema.prisma
├── docker/
│   ├── Dockerfile
│   └── docker-compose.yml
└── client-sdk/
    ├── javascript/
    ├── react/
    └── node/
```

### 2. Database Schema (Minimal)
```sql
-- Core authentication tables
CREATE TABLE auth_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    employee_id VARCHAR(50) UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    tenant_id VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_employee_id (employee_id),
    INDEX idx_tenant (tenant_id)
);

CREATE TABLE auth_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    tenant_id VARCHAR(100) NOT NULL,
    permissions JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_role_tenant (name, tenant_id)
);

CREATE TABLE auth_user_roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    role_id INT NOT NULL,
    tenant_id VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES auth_users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES auth_roles(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_role (user_id, role_id)
);

CREATE TABLE auth_sessions (
    id VARCHAR(255) PRIMARY KEY,
    user_id INT NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES auth_users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires (expires_at)
);
```

### 3. API Endpoints

#### Authentication Endpoints
- `POST /auth/login` - Google OAuth login
- `POST /auth/refresh` - Refresh JWT token
- `POST /auth/logout` - Logout and invalidate session
- `GET /auth/me` - Get current user info
- `POST /auth/validate` - Validate token (for other services)

#### User Management Endpoints
- `GET /users/profile` - Get user profile
- `PUT /users/profile` - Update user profile
- `GET /users/roles` - Get user roles and permissions

#### Admin Endpoints (Optional)
- `GET /admin/users` - List users (admin only)
- `POST /admin/users/{id}/roles` - Assign roles
- `PUT /admin/users/{id}/status` - Activate/deactivate user

### 4. Configuration

#### Environment Variables
```env
# Database
DATABASE_URL=mysql://user:pass@host:port/db

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_WORKSPACE_DOMAIN=tudip.com

# JWT
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=86400
JWT_REFRESH_EXPIRES_IN=604800

# Service
PORT=3000
NODE_ENV=production
CORS_ORIGINS=http://localhost:3000,https://yourapp.com

# Tenant Configuration
DEFAULT_TENANT_ID=tudip
MULTI_TENANT_ENABLED=true
```

### 5. Integration Patterns

#### A. Microservice Integration
```typescript
// Client application calls auth service
const authResponse = await fetch('http://auth-service:3000/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ googleToken: 'oauth-token' })
});
```

#### B. SDK Integration
```typescript
// Using the provided SDK
import { TudipAuth } from '@tudip/auth-sdk';

const auth = new TudipAuth({
  serviceUrl: 'http://auth-service:3000',
  tenantId: 'your-tenant'
});

const user = await auth.loginWithGoogle(googleToken);
```

#### C. Middleware Integration
```typescript
// Express middleware
import { createAuthMiddleware } from '@tudip/auth-middleware';

const authMiddleware = createAuthMiddleware({
  authServiceUrl: 'http://auth-service:3000',
  requiredRoles: ['employee']
});

app.use('/api/protected', authMiddleware);
```

### 6. Security Features

#### Email Domain Validation
- Enforce @tudip.com email domain
- Configurable domain whitelist
- Google Workspace integration

#### Token Security
- JWT with short expiration (1 hour)
- Refresh tokens with longer expiration (7 days)
- Token blacklisting on logout
- Secure HTTP-only cookies option

#### Role-Based Access Control
- Hierarchical role system
- Permission-based authorization
- Tenant-specific roles
- Dynamic permission checking

### 7. Deployment Options

#### Docker Deployment
```yaml
version: '3.8'
services:
  auth-service:
    image: tudip/auth-service:latest
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mysql

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: auth_service
```

#### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tudip-auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tudip-auth-service
  template:
    metadata:
      labels:
        app: tudip-auth-service
    spec:
      containers:
      - name: auth-service
        image: tudip/auth-service:latest
        ports:
        - containerPort: 3000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: database-url
```

### 8. Client SDKs

#### React SDK
```typescript
// React hooks for authentication
import { useAuth, AuthProvider } from '@tudip/auth-react';

function App() {
  return (
    <AuthProvider authServiceUrl="http://auth-service:3000">
      <MyApp />
    </AuthProvider>
  );
}

function MyComponent() {
  const { user, login, logout, isAuthenticated } = useAuth();
  
  return (
    <div>
      {isAuthenticated ? (
        <div>Welcome {user.firstName}!</div>
      ) : (
        <button onClick={login}>Login with Tudip</button>
      )}
    </div>
  );
}
```

#### Node.js SDK
```typescript
// Server-side authentication
import { TudipAuthClient } from '@tudip/auth-node';

const authClient = new TudipAuthClient({
  serviceUrl: 'http://auth-service:3000',
  apiKey: 'your-api-key'
});

// Validate token
const user = await authClient.validateToken(token);

// Check permissions
const hasPermission = await authClient.checkPermission(
  userId, 
  'read:documents'
);
```

### 9. Migration Strategy

#### Phase 1: Service Development
1. Build core authentication service
2. Implement Google OAuth integration
3. Create basic user management
4. Develop JWT token system

#### Phase 2: Integration Testing
1. Create test applications
2. Develop client SDKs
3. Test authentication flows
4. Performance testing

#### Phase 3: Production Deployment
1. Deploy to staging environment
2. Migrate existing applications
3. Monitor and optimize
4. Documentation and training

### 10. Benefits

#### For Applications
- Consistent authentication across all Tudip applications
- Reduced development time
- Centralized user management
- Enhanced security

#### For Users
- Single sign-on experience
- Consistent UI/UX
- Centralized profile management
- Better security

#### For IT/Security
- Centralized access control
- Audit logging
- Security policy enforcement
- Easier compliance management
