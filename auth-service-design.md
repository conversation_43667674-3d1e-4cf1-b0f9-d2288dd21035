# Simplified Reusable Tudip Authentication Service

## Architecture Overview

This design transforms the existing Kuber application's authentication system into a simplified reusable service that other Tudip applications can consume for basic authentication. The service focuses solely on Google OAuth authentication and user validation against the existing Kuber users database, without complex role management or user administration features.

## Simplified Kuber Backend Structure

### 1. Minimal Authentication Components
```
kuber-backend/
├── src/
│   ├── google-auth/                    # Existing auth module (enhanced)
│   │   ├── auth.module.ts
│   │   ├── auth.service.ts
│   │   ├── auth.controller.ts
│   │   ├── external-auth.controller.ts  # NEW: External API endpoints
│   │   ├── jwt.strategy.ts
│   │   └── dto/
│   │       ├── external-auth.dto.ts     # NEW: External API DTOs
│   │       └── swaggerTokenVerificationDTO.ts
│   ├── users/                          # Existing users module (minimal changes)
│   │   ├── users.module.ts
│   │   ├── users.service.ts
│   │   └── users.controller.ts
│   ├── auth-sdk/                       # NEW: SDK generation module
│   │   ├── sdk.module.ts
│   │   ├── sdk.service.ts
│   │   ├── sdk.controller.ts
│   │   └── templates/
│   │       ├── react-sdk.template.ts
│   │       ├── node-sdk.template.ts
│   │       └── javascript-sdk.template.ts
│   ├── external-api/                   # NEW: External API management
│   │   ├── external-api.module.ts
│   │   ├── api-key.service.ts
│   │   └── middleware/
│   │       ├── api-key.middleware.ts
│   │       └── rate-limiting.middleware.ts
│   └── ... (existing modules)
└── client-sdks/                        # NEW: Generated SDK outputs
    ├── react/
    ├── node/
    ├── javascript/
    └── documentation/
```

### 2. Minimal Database Schema Extensions

The existing Kuber database schema will be extended with minimal tables to support external API access:

```sql
-- New tables to add to existing Kuber database

-- API Keys for external applications
CREATE TABLE external_api_keys (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_name VARCHAR(100) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    allowed_origins TEXT, -- JSON array of allowed origins
    rate_limit_per_hour INT DEFAULT 1000,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_api_key (api_key),
    INDEX idx_application (application_name)
);

-- External API usage tracking (optional)
CREATE TABLE external_api_usage (
    id INT PRIMARY KEY AUTO_INCREMENT,
    api_key_id INT NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    user_email VARCHAR(255), -- Track which user was authenticated
    response_status INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (api_key_id) REFERENCES external_api_keys(id) ON DELETE CASCADE,
    INDEX idx_api_key_date (api_key_id, created_at),
    INDEX idx_user_email (user_email)
);

-- Existing tables used as-is:
-- users table: Contains all Tudip employees (username = email, first_name, last_name, employee_id, is_active)
-- No role or permission tables needed for simplified authentication
```

### 3. Simplified API Endpoints

#### Internal Endpoints (Existing - Unchanged)
These endpoints serve the Kuber application and remain unchanged:

- `POST /google-authentication` - Google OAuth login (existing)
- `GET /users/profile` - User profile endpoint (existing)

#### External API Endpoints (New - Simplified)
These new endpoints allow other Tudip applications to consume Kuber's authentication:

**Authentication API (`/external/auth`)**
- `POST /external/auth/login` - Google OAuth login for external apps
  - Input: Google OAuth token
  - Output: JWT token + basic user info OR "User not present in the organization" error
- `POST /external/auth/validate` - Validate JWT token
  - Input: JWT token
  - Output: User info if valid, error if invalid
- `GET /external/auth/user-info` - Get user information from token
  - Input: JWT token in Authorization header
  - Output: Basic user info (name, email, employee ID)

**SDK and Documentation (`/external/sdk`)**
- `GET /external/sdk/download/{type}` - Download SDK (react/node/js)
- `GET /external/sdk/documentation` - API documentation
- `GET /external/sdk/examples` - Integration examples

**API Management (`/external/admin`)**
- `POST /external/admin/api-keys` - Generate API keys for applications
- `GET /external/admin/api-keys` - List API keys
- `PUT /external/admin/api-keys/{id}/status` - Activate/deactivate API key

### 4. Simplified Configuration

#### Additional Environment Variables
Add these to the existing Kuber backend `.env` file:

```env
# Existing Kuber configuration remains unchanged
# DATABASE_URL, GOOGLE_CLIENT_ID, GOOGLE_SECRET, JWT_SECRET, etc.

# External API Configuration
EXTERNAL_API_ENABLED=true
EXTERNAL_API_PREFIX=/external
EXTERNAL_API_RATE_LIMIT=1000  # requests per hour per API key
EXTERNAL_API_CORS_ORIGINS=*   # or specific domains

# SDK Configuration
SDK_VERSION=1.0.0
SDK_BASE_URL=https://kuber.tudip.com

# API Key Configuration
API_KEY_LENGTH=32
DEFAULT_API_KEY_EXPIRY_DAYS=365

# Documentation
SWAGGER_EXTERNAL_ENABLED=true
SWAGGER_EXTERNAL_PATH=/external/docs
```

### 5. Simplified Integration Patterns

#### A. Direct API Integration
```typescript
// External application calls Kuber's external API
const authResponse = await fetch('https://kuber.tudip.com/external/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your-api-key'
  },
  body: JSON.stringify({ googleToken: 'oauth-token' })
});

// Response: Success with user info OR "User not present in the organization" error
const result = await authResponse.json();
if (result.success) {
  // User exists in Kuber database
  const { token, user } = result;
  // user contains: { email, firstName, lastName, employeeId }
} else {
  // User not found in organization
  console.error(result.message); // "User not present in the organization"
}

// Validate token in external application
const validation = await fetch('https://kuber.tudip.com/external/auth/validate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your-api-key'
  },
  body: JSON.stringify({ token: 'jwt-token' })
});
```

#### B. React SDK Integration
```typescript
// Generated React SDK from Kuber
import { TudipAuthProvider, useTudipAuth } from '@tudip/kuber-auth-react';

function App() {
  return (
    <TudipAuthProvider
      apiKey="your-api-key"
      kuberUrl="https://kuber.tudip.com"
    >
      <MyApp />
    </TudipAuthProvider>
  );
}

function LoginComponent() {
  const { user, login, logout, isAuthenticated, error } = useTudipAuth();

  return (
    <div>
      {isAuthenticated ? (
        <div>Welcome {user.firstName}! <button onClick={logout}>Logout</button></div>
      ) : (
        <div>
          <button onClick={login}>Login with Tudip</button>
          {error && <p style={{color: 'red'}}>{error}</p>}
        </div>
      )}
    </div>
  );
}
```

#### C. Node.js SDK Integration
```typescript
// Generated Node.js SDK from Kuber
import { KuberAuthClient } from '@tudip/kuber-auth-node';

const authClient = new KuberAuthClient({
  apiKey: 'your-api-key',
  kuberUrl: 'https://kuber.tudip.com'
});

// Simple middleware for Express applications
app.use('/api/protected', authClient.middleware());

// Manual token validation
try {
  const user = await authClient.validateToken(req.headers.authorization);
  // user contains: { email, firstName, lastName, employeeId }
} catch (error) {
  // Token invalid or user not in organization
  res.status(401).json({ error: error.message });
}
```

#### D. JavaScript SDK Integration
```javascript
// Vanilla JavaScript SDK from Kuber
import { KuberAuth } from '@tudip/kuber-auth-js';

const auth = new KuberAuth({
  apiKey: 'your-api-key',
  kuberUrl: 'https://kuber.tudip.com'
});

// Login with Google
try {
  const user = await auth.loginWithGoogle();
  console.log('Logged in user:', user);
} catch (error) {
  if (error.message === 'User not present in the organization') {
    alert('You are not authorized to access this application.');
  }
}

// Check authentication status
if (auth.isAuthenticated()) {
  const user = auth.getCurrentUser();
  console.log('Current user:', user); // { email, firstName, lastName, employeeId }
}
```

### 6. Simplified Security Features

#### Email Domain Validation (Existing)
- Enforce @tudip.com email domain (existing)
- Google Workspace integration (existing)
- User validation against existing Kuber users database

#### Token Security (Existing + Minimal Enhancements)
- JWT with configurable expiration (existing)
- **NEW**: API key authentication for external access
- **NEW**: Rate limiting per API key
- **NEW**: Basic CORS control

#### User Validation Logic
- **Google OAuth Validation**: Verify Google token is valid and from @tudip.com domain
- **Organization Membership Check**: Check if user exists in Kuber users table
- **Active User Validation**: Ensure user account is active (is_active = 1)
- **Response Logic**:
  - If user exists and is active: Return JWT token with user info
  - If user doesn't exist: Return "User not present in the organization" error
  - If user exists but inactive: Return "User account is deactivated" error

#### External API Security
- **API Key Authentication**: Each external application gets unique API key
- **Rate Limiting**: Configurable requests per hour per API key
- **CORS Control**: Basic allowed origins configuration
- **Audit Logging**: Track authentication attempts and user access
- **Token Validation**: External tokens validated against Kuber's user database
- **Minimal Scope**: External APIs only provide authentication, no user management

### 7. Deployment Strategy

#### Enhanced Kuber Backend Deployment
The existing Kuber application deployment remains unchanged, with additional configuration:

```yaml
# Enhanced docker-compose.yml for Kuber backend
version: '3.8'
services:
  kuber-backend:
    build: ./kuber-backend
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - JWT_SECRET=${JWT_SECRET}
      # New external API configuration
      - EXTERNAL_API_ENABLED=true
      - EXTERNAL_API_RATE_LIMIT=1000
      - SDK_VERSION=1.0.0
    depends_on:
      - mysql
    volumes:
      - ./client-sdks:/app/client-sdks  # Mount SDK output directory

  kuber-frontend:
    build: ./kuber-frontend
    ports:
      - "3000:3000"
    depends_on:
      - kuber-backend

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: kuber
```

#### Production Considerations
- **Load Balancing**: External API endpoints can be load balanced separately
- **CDN Integration**: SDK files can be served via CDN
- **SSL/TLS**: HTTPS required for external API access
- **Monitoring**: Enhanced logging for external API usage
- **Backup**: Regular database backups including new external API tables

### 8. Simplified SDK Generation

#### Automated SDK Generation
Kuber backend will include a simplified SDK generation service:

```typescript
// Simplified SDK Generation Service
@Injectable()
export class SdkService {
  generateReactSdk(apiKey: string, version: string): string {
    // Generate React SDK with API key embedded
    // Simple hooks: useTudipAuth()
    // Basic components: TudipAuthProvider
  }

  generateNodeSdk(apiKey: string, version: string): string {
    // Generate Node.js SDK with authentication helpers
    // Simple middleware for token validation
    // Basic user validation functions
  }

  generateJavaScriptSdk(apiKey: string, version: string): string {
    // Generate vanilla JavaScript SDK
    // Simple authentication class
    // Browser-compatible Google OAuth integration
  }

  generateDocumentation(version: string): string {
    // Generate simple API documentation
    // Basic integration examples
    // Error handling guide
  }
}
```

#### SDK Distribution
- **Download Endpoint**: `/external/sdk/download/{type}`
- **Simple Files**: Direct download of SDK files
- **Documentation**: Basic integration guide and examples

#### SDK Features
- **Pre-configured**: API keys embedded during generation
- **Simple Interface**: Minimal API surface for authentication only
- **Error Handling**: Clear error messages for organization membership
- **Token Management**: Basic JWT token storage and validation
- **Framework Agnostic**: Works with any frontend framework

### 9. Simplified Migration Strategy

#### Phase 1: Backend Enhancement (1-2 weeks)
1. **Add External API Module**: Create simple external authentication endpoints
2. **Database Migration**: Add minimal external API tables (api_keys, usage tracking)
3. **Basic Security**: Add API key authentication and rate limiting
4. **Testing**: Basic tests for authentication flow

#### Phase 2: SDK Development (1-2 weeks)
1. **SDK Generation Service**: Build simple SDK generation
2. **React SDK**: Create basic React hooks for authentication
3. **Node.js SDK**: Build simple token validation middleware
4. **JavaScript SDK**: Basic browser authentication library
5. **Documentation**: Simple integration guide

#### Phase 3: Pilot Integration (1-2 weeks)
1. **Select Pilot Application**: Choose one simple Tudip application
2. **API Key Generation**: Create API key for pilot application
3. **SDK Integration**: Test basic authentication flow
4. **User Validation**: Test "User not present in organization" flow
5. **Bug Fixes**: Address any integration issues

#### Phase 4: Rollout (2-3 weeks)
1. **Documentation**: Complete basic integration documentation
2. **Team Training**: Train teams on simple authentication integration
3. **Gradual Migration**: Migrate applications one by one
4. **Support**: Provide basic integration support
5. **Monitoring**: Monitor authentication success/failure rates

#### Phase 5: Maintenance (Ongoing)
1. **Bug Fixes**: Address any authentication issues
2. **Security Updates**: Keep Google OAuth integration updated
3. **User Database Sync**: Ensure user database stays current
4. **Performance Monitoring**: Monitor API response times

### 10. Simplified Benefits

#### For Applications
- **Consistent Authentication**: Same Google OAuth flow across all Tudip applications
- **Reduced Development Time**: Pre-built SDKs eliminate authentication development
- **Simple Integration**: Basic authentication with clear error messages
- **Organization Validation**: Automatic check against Tudip employee database
- **No User Management**: Applications don't need to manage user data

#### For Users
- **Familiar Login**: Same Google OAuth login across all applications
- **Clear Feedback**: Clear error message if not authorized for application
- **Single Identity**: One Tudip identity across all applications
- **No Multiple Accounts**: No need to create separate accounts per application

#### For IT/Security
- **Centralized User Control**: Manage user access from Kuber user database
- **Simple Audit**: Track authentication attempts across applications
- **Easy User Removal**: Remove user from Kuber to revoke all application access
- **Consistent Security**: Same security standards across all applications
- **Quick Response**: Deactivate users instantly across all applications

#### For Development Teams
- **Simple Implementation**: Basic authentication with minimal complexity
- **Clear Documentation**: Straightforward integration guides
- **Minimal Maintenance**: No complex role or permission management
- **Fast Integration**: Quick setup with generated SDKs
- **Reliable Service**: Leverages proven Kuber authentication system

### 11. Implementation Considerations

#### Backward Compatibility
- Existing Kuber application functionality remains completely unchanged
- Internal APIs continue to work exactly as before
- External APIs are purely additive features

#### Performance Impact
- External API endpoints are lightweight and focused
- Rate limiting prevents abuse
- Simple database queries against existing users table
- Minimal overhead on existing Kuber performance

#### Maintenance
- Simple codebase with minimal complexity
- Automated SDK generation reduces manual work
- Clear error handling and logging
- Easy to troubleshoot authentication issues

#### User Database Sync
- External applications rely on Kuber's users table
- HR team continues to manage users through Kuber admin
- User additions/removals automatically affect all applications
- No complex synchronization needed
