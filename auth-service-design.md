# Reusable Tudip Authentication Service Integration

## Architecture Overview

This design transforms the existing Kuber application's authentication system into a reusable service that other Tudip applications can consume. Instead of creating a separate microservice, we'll modularize and expose the current Google OAuth authentication system through well-defined APIs and SDKs while maintaining the existing Kuber application structure.

## Enhanced Kuber Backend Structure

### 1. Modularized Authentication Components
```
kuber-backend/
├── src/
│   ├── google-auth/                    # Existing auth module (enhanced)
│   │   ├── auth.module.ts
│   │   ├── auth.service.ts
│   │   ├── auth.controller.ts
│   │   ├── external-auth.controller.ts  # NEW: External API endpoints
│   │   ├── jwt.strategy.ts
│   │   └── dto/
│   │       ├── external-auth.dto.ts     # NEW: External API DTOs
│   │       └── swaggerTokenVerificationDTO.ts
│   ├── users/                          # Existing users module (enhanced)
│   │   ├── users.module.ts
│   │   ├── users.service.ts
│   │   ├── users.controller.ts
│   │   └── external-users.controller.ts # NEW: External user API
│   ├── auth-sdk/                       # NEW: SDK generation module
│   │   ├── sdk.module.ts
│   │   ├── sdk.service.ts
│   │   ├── sdk.controller.ts
│   │   └── templates/
│   │       ├── react-sdk.template.ts
│   │       ├── node-sdk.template.ts
│   │       └── javascript-sdk.template.ts
│   ├── external-api/                   # NEW: External API management
│   │   ├── external-api.module.ts
│   │   ├── api-key.service.ts
│   │   ├── rate-limiting.service.ts
│   │   └── middleware/
│   │       ├── api-key.middleware.ts
│   │       └── external-cors.middleware.ts
│   └── ... (existing modules)
└── client-sdks/                        # NEW: Generated SDK outputs
    ├── react/
    ├── node/
    ├── javascript/
    └── documentation/
```

### 2. Database Schema Extensions

The existing Kuber database schema will be extended with additional tables to support external API access and SDK management:

```sql
-- New tables to add to existing Kuber database

-- API Keys for external applications
CREATE TABLE external_api_keys (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_name VARCHAR(100) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    api_secret VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    allowed_origins TEXT, -- JSON array of allowed origins
    rate_limit_per_hour INT DEFAULT 1000,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_api_key (api_key),
    INDEX idx_application (application_name)
);

-- External API usage tracking
CREATE TABLE external_api_usage (
    id INT PRIMARY KEY AUTO_INCREMENT,
    api_key_id INT NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    response_status INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (api_key_id) REFERENCES external_api_keys(id) ON DELETE CASCADE,
    INDEX idx_api_key_date (api_key_id, created_at),
    INDEX idx_endpoint (endpoint)
);

-- SDK download tracking
CREATE TABLE sdk_downloads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sdk_type ENUM('react', 'node', 'javascript', 'documentation') NOT NULL,
    version VARCHAR(20) NOT NULL,
    downloaded_by INT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (downloaded_by) REFERENCES users(id),
    INDEX idx_sdk_type (sdk_type),
    INDEX idx_version (version)
);

-- Existing tables remain unchanged:
-- users, roles, user_roles (already support multi-tenancy and authentication)
```

### 3. API Endpoints

#### Internal Endpoints (Existing - Enhanced)
These endpoints serve the Kuber application and are enhanced for better reusability:

- `POST /google-authentication` - Enhanced Google OAuth login (existing)
- `GET /users/profile` - Enhanced user profile endpoint
- `GET /users/roles` - User roles and permissions

#### External API Endpoints (New)
These new endpoints allow other Tudip applications to consume Kuber's authentication:

**Authentication API (`/external/auth`)**
- `POST /external/auth/login` - Google OAuth login for external apps
- `POST /external/auth/validate` - Validate JWT token
- `POST /external/auth/refresh` - Refresh access token
- `GET /external/auth/user-info` - Get user information from token

**User Management API (`/external/users`)**
- `GET /external/users/profile` - Get user profile by token
- `GET /external/users/roles` - Get user roles and permissions
- `GET /external/users/search` - Search users (admin only)

**SDK and Documentation (`/external/sdk`)**
- `GET /external/sdk/download/{type}` - Download SDK (react/node/js)
- `GET /external/sdk/documentation` - API documentation
- `GET /external/sdk/examples` - Integration examples

**API Management (`/external/admin`)**
- `POST /external/admin/api-keys` - Generate API keys for applications
- `GET /external/admin/api-keys` - List API keys
- `PUT /external/admin/api-keys/{id}` - Update API key settings
- `GET /external/admin/usage` - API usage statistics

### 4. Configuration Enhancements

#### Additional Environment Variables
Add these to the existing Kuber backend `.env` file:

```env
# Existing Kuber configuration remains unchanged
# DATABASE_URL, GOOGLE_CLIENT_ID, GOOGLE_SECRET, JWT_SECRET, etc.

# External API Configuration
EXTERNAL_API_ENABLED=true
EXTERNAL_API_PREFIX=/external
EXTERNAL_API_RATE_LIMIT=1000  # requests per hour per API key
EXTERNAL_API_CORS_ORIGINS=*   # or specific domains

# SDK Configuration
SDK_VERSION=1.0.0
SDK_BASE_URL=https://kuber.tudip.com
SDK_DOWNLOAD_PATH=/external/sdk

# API Key Configuration
API_KEY_LENGTH=32
API_SECRET_LENGTH=64
DEFAULT_API_KEY_EXPIRY_DAYS=365

# Documentation
SWAGGER_EXTERNAL_ENABLED=true
SWAGGER_EXTERNAL_PATH=/external/docs
```

### 5. Integration Patterns for External Applications

#### A. Direct API Integration
```typescript
// External application calls Kuber's external API
const authResponse = await fetch('https://kuber.tudip.com/external/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your-api-key',
    'X-API-Secret': 'your-api-secret'
  },
  body: JSON.stringify({ googleToken: 'oauth-token' })
});

// Validate token in external application
const validation = await fetch('https://kuber.tudip.com/external/auth/validate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your-api-key'
  },
  body: JSON.stringify({ token: 'jwt-token' })
});
```

#### B. React SDK Integration
```typescript
// Generated React SDK from Kuber
import { TudipAuthProvider, useTudipAuth } from '@tudip/kuber-auth-react';

function App() {
  return (
    <TudipAuthProvider
      apiKey="your-api-key"
      apiSecret="your-api-secret"
      kuberUrl="https://kuber.tudip.com"
    >
      <MyApp />
    </TudipAuthProvider>
  );
}

function LoginComponent() {
  const { user, login, logout, isAuthenticated } = useTudipAuth();

  return (
    <div>
      {isAuthenticated ? (
        <div>Welcome {user.firstName}! <button onClick={logout}>Logout</button></div>
      ) : (
        <button onClick={login}>Login with Tudip</button>
      )}
    </div>
  );
}
```

#### C. Node.js SDK Integration
```typescript
// Generated Node.js SDK from Kuber
import { KuberAuthClient } from '@tudip/kuber-auth-node';

const authClient = new KuberAuthClient({
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret',
  kuberUrl: 'https://kuber.tudip.com'
});

// Middleware for Express applications
app.use('/api/protected', authClient.middleware({
  requiredRoles: ['employee']
}));

// Manual token validation
const user = await authClient.validateToken(req.headers.authorization);
```

#### D. JavaScript SDK Integration
```javascript
// Vanilla JavaScript SDK from Kuber
import { KuberAuth } from '@tudip/kuber-auth-js';

const auth = new KuberAuth({
  apiKey: 'your-api-key',
  kuberUrl: 'https://kuber.tudip.com'
});

// Login with Google
const user = await auth.loginWithGoogle();

// Check authentication status
if (auth.isAuthenticated()) {
  console.log('User:', auth.getCurrentUser());
}
```

### 6. Security Features (Enhanced)

#### Email Domain Validation (Existing + Enhanced)
- Enforce @tudip.com email domain (existing)
- Configurable domain whitelist (existing)
- Google Workspace integration (existing)
- **NEW**: External API domain validation per API key

#### Token Security (Existing + Enhanced)
- JWT with configurable expiration (existing)
- **NEW**: Separate token expiration for external APIs
- **NEW**: API key + secret authentication for external access
- **NEW**: Rate limiting per API key
- **NEW**: IP whitelisting for API keys

#### Role-Based Access Control (Existing + Enhanced)
- Hierarchical role system (existing)
- Permission-based authorization (existing)
- Tenant-specific roles (existing)
- **NEW**: External API permission scoping
- **NEW**: Application-specific role restrictions

#### External API Security
- **API Key Authentication**: Each external application gets unique API key/secret
- **Rate Limiting**: Configurable requests per hour per API key
- **CORS Control**: Configurable allowed origins per API key
- **Audit Logging**: All external API calls logged with application context
- **Token Validation**: External tokens validated against Kuber's user database
- **Scope Restrictions**: External APIs have limited access compared to internal APIs

### 7. Deployment Strategy

#### Enhanced Kuber Backend Deployment
The existing Kuber application deployment remains unchanged, with additional configuration:

```yaml
# Enhanced docker-compose.yml for Kuber backend
version: '3.8'
services:
  kuber-backend:
    build: ./kuber-backend
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - JWT_SECRET=${JWT_SECRET}
      # New external API configuration
      - EXTERNAL_API_ENABLED=true
      - EXTERNAL_API_RATE_LIMIT=1000
      - SDK_VERSION=1.0.0
    depends_on:
      - mysql
    volumes:
      - ./client-sdks:/app/client-sdks  # Mount SDK output directory

  kuber-frontend:
    build: ./kuber-frontend
    ports:
      - "3000:3000"
    depends_on:
      - kuber-backend

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: kuber
```

#### Production Considerations
- **Load Balancing**: External API endpoints can be load balanced separately
- **CDN Integration**: SDK files can be served via CDN
- **SSL/TLS**: HTTPS required for external API access
- **Monitoring**: Enhanced logging for external API usage
- **Backup**: Regular database backups including new external API tables

### 8. SDK Generation and Distribution

#### Automated SDK Generation
Kuber backend will include an SDK generation service that creates client libraries:

```typescript
// SDK Generation Service (New)
@Injectable()
export class SdkService {
  generateReactSdk(apiKey: string, version: string): string {
    // Generate React SDK with API key embedded
    // Include TypeScript definitions
    // Package as npm-ready module
  }

  generateNodeSdk(apiKey: string, version: string): string {
    // Generate Node.js SDK with authentication helpers
    // Include middleware functions
    // Package as npm-ready module
  }

  generateJavaScriptSdk(apiKey: string, version: string): string {
    // Generate vanilla JavaScript SDK
    // Include UMD and ES6 module formats
    // Browser-compatible authentication
  }

  generateDocumentation(version: string): string {
    // Generate API documentation
    // Include integration examples
    // Interactive API explorer
  }
}
```

#### SDK Distribution
- **Download Endpoint**: `/external/sdk/download/{type}?version={version}`
- **NPM Packages**: Auto-published to private NPM registry
- **CDN Distribution**: SDKs available via CDN for browser usage
- **Version Management**: Semantic versioning with backward compatibility

#### SDK Features
- **Auto-configuration**: API keys embedded during generation
- **Type Safety**: Full TypeScript support
- **Error Handling**: Comprehensive error handling and retry logic
- **Caching**: Built-in token caching and refresh
- **Framework Integration**: Specific integrations for React, Vue, Angular

### 9. Migration Strategy

#### Phase 1: Backend Enhancement (2-3 weeks)
1. **Add External API Module**: Create new external API controllers and services
2. **Database Migration**: Add external API tables (api_keys, usage tracking, etc.)
3. **Security Implementation**: Add API key authentication and rate limiting
4. **Testing**: Unit and integration tests for new external endpoints

#### Phase 2: SDK Development (2-3 weeks)
1. **SDK Generation Service**: Build automated SDK generation
2. **React SDK**: Create React hooks and components
3. **Node.js SDK**: Build server-side authentication helpers
4. **JavaScript SDK**: Vanilla JS library for browser usage
5. **Documentation**: API docs and integration guides

#### Phase 3: Pilot Integration (2-4 weeks)
1. **Select Pilot Application**: Choose one Tudip application for testing
2. **API Key Generation**: Create API keys for pilot application
3. **SDK Integration**: Integrate generated SDK into pilot application
4. **Testing and Refinement**: Test authentication flows and fix issues
5. **Performance Monitoring**: Monitor API usage and performance

#### Phase 4: Rollout (4-6 weeks)
1. **Documentation**: Complete integration documentation
2. **Training**: Train development teams on SDK usage
3. **Gradual Migration**: Migrate applications one by one
4. **Support**: Provide integration support and troubleshooting
5. **Monitoring**: Monitor usage across all integrated applications

#### Phase 5: Optimization (Ongoing)
1. **Performance Tuning**: Optimize based on usage patterns
2. **Feature Enhancement**: Add new features based on feedback
3. **Security Audits**: Regular security reviews and updates
4. **SDK Updates**: Regular SDK updates and improvements

### 10. Benefits

#### For Applications
- **Consistent Authentication**: Same Google OAuth flow across all Tudip applications
- **Reduced Development Time**: Pre-built SDKs eliminate authentication development
- **Centralized User Management**: Single source of truth for user data
- **Enhanced Security**: Leverages Kuber's proven security implementation
- **Easy Integration**: Multiple SDK options for different technology stacks
- **Automatic Updates**: SDK updates propagate security improvements

#### For Users
- **Single Sign-On Experience**: Login once, access all Tudip applications
- **Consistent UI/UX**: Same login experience across applications
- **Centralized Profile Management**: Update profile in one place
- **Better Security**: Centralized security policies and monitoring
- **Seamless Experience**: No need to remember multiple credentials

#### For IT/Security
- **Centralized Access Control**: Manage user access from Kuber admin panel
- **Comprehensive Audit Logging**: All authentication events logged centrally
- **Security Policy Enforcement**: Consistent security policies across applications
- **Easier Compliance Management**: Single point for compliance audits
- **Real-time Monitoring**: Monitor authentication usage across all applications
- **Quick Security Response**: Disable users or API keys instantly across all apps

#### For Development Teams
- **Faster Time to Market**: No need to build authentication from scratch
- **Reduced Maintenance**: Authentication updates handled centrally
- **Better Documentation**: Comprehensive guides and examples
- **Support**: Dedicated support for integration issues
- **Flexibility**: Multiple integration patterns to choose from

### 11. Implementation Considerations

#### Backward Compatibility
- Existing Kuber application functionality remains unchanged
- Internal APIs continue to work as before
- External APIs are additive, not replacing existing functionality

#### Performance Impact
- External API endpoints are separate from internal application logic
- Rate limiting prevents abuse and ensures performance
- Caching strategies for frequently accessed user data
- Database indexing optimized for external API queries

#### Maintenance
- SDK generation is automated, reducing manual maintenance
- Version management ensures backward compatibility
- Automated testing for all SDK versions
- Clear deprecation policies for API changes
