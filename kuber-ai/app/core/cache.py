import redis
import json
import hashlib
from typing import Optional, Any
from app.core.config import settings

class CacheManager:
    def __init__(self):
        self.redis_client = redis.Redis(
            host=settings.redis_host or 'localhost',
            port=settings.redis_port or 6379,
            db=0,
            decode_responses=True
        )
        self.default_ttl = 300  # 5 minutes

    def _generate_key(self, prefix: str, data: str) -> str:
        """Generate cache key from data hash"""
        hash_obj = hashlib.md5(data.encode())
        return f"{prefix}:{hash_obj.hexdigest()}"

    async def get_cached_response(self, user_id: int, message: str) -> Optional[dict]:
        """Get cached response for similar queries"""
        try:
            key = self._generate_key(f"chat:{user_id}", message.lower().strip())
            cached = self.redis_client.get(key)
            return json.loads(cached) if cached else None
        except Exception:
            return None

    async def cache_response(self, user_id: int, message: str, response: dict, ttl: int = None):
        """Cache response for future use"""
        try:
            key = self._generate_key(f"chat:{user_id}", message.lower().strip())
            self.redis_client.setex(
                key, 
                ttl or self.default_ttl, 
                json.dumps(response)
            )
        except Exception:
            pass

    async def get_user_context(self, user_id: int) -> Optional[dict]:
        """Get user conversation context"""
        try:
            key = f"context:{user_id}"
            cached = self.redis_client.get(key)
            return json.loads(cached) if cached else None
        except Exception:
            return None

    async def set_user_context(self, user_id: int, context: dict, ttl: int = 1800):
        """Set user conversation context (30 min TTL)"""
        try:
            key = f"context:{user_id}"
            self.redis_client.setex(key, ttl, json.dumps(context))
        except Exception:
            pass