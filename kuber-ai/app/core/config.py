from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    jwt_secret: str = "your_jwt_secret"
    jwt_algorithm: str = "HS256"
    gemini_api_key: str = "your_gemini_api_key"
    gemini_api_url: str = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
    # Add other config variables as needed

    class Config:
        env_file = ".env"

settings = Settings()
