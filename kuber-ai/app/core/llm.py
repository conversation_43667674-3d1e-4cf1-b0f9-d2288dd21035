import httpx
from app.core.config import settings

async def query_gemini(prompt: str) -> str:
    """
    Sends a prompt to the Gemini LLM API and returns the response text.
    """
    url = f"{settings.gemini_api_url}?key={settings.gemini_api_key}"
    headers = {"Content-Type": "application/json"}
    payload = {
        "contents": [
            {"parts": [{"text": prompt}]}
        ]
    }
    async with httpx.AsyncClient() as client:
        response = await client.post(url, json=payload, headers=headers)
        response.raise_for_status()
        data = response.json()
        # Extract the generated text from Gemini's response
        try:
            return data["candidates"][0]["content"]["parts"][0]["text"]
        except Exception:
            return "Sorry, I couldn't generate a response."
