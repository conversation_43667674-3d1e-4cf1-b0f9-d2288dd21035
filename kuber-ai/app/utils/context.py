# In-memory conversation context management for kuber-ai

from typing import Dict, Any

# For production, replace with Redis or persistent store
user_contexts: Dict[str, Dict[str, Any]] = {}

def get_context(user_id: str) -> Dict[str, Any]:
    return user_contexts.get(user_id, {})

def set_context(user_id: str, context: Dict[str, Any]):
    user_contexts[user_id] = context

def clear_context(user_id: str):
    user_contexts.pop(user_id, None)
