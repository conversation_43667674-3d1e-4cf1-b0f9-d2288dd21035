from fastapi import Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt
from app.core.config import settings

security = HTTPBearer()

JWT_SECRET = settings.jwt_secret
JWT_ALGORITHM = settings.jwt_algorithm

from fastapi import Depends, HTTPException, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt
from app.core.config import settings

security = HTTPBearer()

JWT_SECRET = settings.jwt_secret
JWT_ALGORITHM = settings.jwt_algorithm

def get_current_user(authorization: str = Header(None)):
    if not authorization or not authorization.lower().startswith("bearer "):
        raise HTTPException(status_code=401, detail="Authorization header missing or invalid")
    token = authorization.split(" ", 1)[1]
    try:
        # WARNING: The following disables exp validation for testing only!
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM], options={"verify_exp": False})
        if not payload.get("userId"):
            print("JWT validation failed: userId missing in token payload")
            raise HTTPException(status_code=401, detail="Invalid token: userId missing")
        return payload
    except Exception as e:
        print(f"JWT validation failed: {str(e)}")
        raise HTTPException(status_code=401, detail="Invalid or expired token")
