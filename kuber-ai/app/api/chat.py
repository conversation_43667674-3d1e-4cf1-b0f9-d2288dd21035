from fastapi import APIRouter, Request, UploadFile, File, Header, Query
from app.models.schemas import ChatRequest, ChatResponse
from app.core.llm import query_gemini
from jose import jwt
from app.core.config import settings
import time
import hashlib
from typing import Dict, Optional

router = APIRouter()

# In-memory cache with TTL
response_cache: Dict[str, dict] = {}
user_context: Dict[int, dict] = {}

from app.api.auth import get_current_user
from fastapi import Depends

def get_cache_key(user_id: int, message: str) -> str:
    """Generate cache key"""
    return hashlib.md5(f"{user_id}:{message.lower().strip()}".encode()).hexdigest()

def clean_cache():
    """Remove expired cache entries"""
    current_time = time.time()
    expired_keys = [k for k, v in response_cache.items() if current_time - v.get('timestamp', 0) > 300]
    for key in expired_keys:
        response_cache.pop(key, None)

@router.post("/message", response_model=ChatResponse)
async def chat_message(
    req: ChatRequest,
    user=Depends(get_current_user)
):
    user_id = user.get("userId")
    message = req.message.strip()
    
    # Clean expired cache periodically
    clean_cache()
    
    # Check cache first for common queries
    cache_key = get_cache_key(user_id, message)
    if cache_key in response_cache:
        cached = response_cache[cache_key]
        if time.time() - cached['timestamp'] < 300:  # 5 min TTL
            return ChatResponse(**cached['response'])
    
    start_time = time.time()
    
    # Special case: direct data queries
    if message.startswith("Given the following user homepage data as JSON:"):
        response = await query_gemini(message)
        result = ChatResponse(response=response.strip(), data={"userId": user_id})
        
        # Cache the response
        response_cache[cache_key] = {
            'response': result.dict(),
            'timestamp': time.time()
        }
        return result
    
    # Quick intent detection for common patterns
    message_lower = message.lower()
    if any(word in message_lower for word in ['hi', 'hello', 'hey']):
        return ChatResponse(
            response="Hello! How can I assist you today?",
            data={"userId": user_id, "processing_time": int((time.time() - start_time) * 1000)}
        )
    
    # Default to user detail intent for faster processing
    field = "personal"
    if "leave" in message_lower and "balance" in message_lower:
        field = "leave_balance"
    elif "manager" in message_lower:
        field = "manager"
    elif "designation" in message_lower or "role" in message_lower:
        field = "designation"
    
    result = ChatResponse(
        response=f"Fetching your {field.replace('_', ' ')}...",
        data={
            "userId": user_id, 
            "intent": "user_detail", 
            "field": field,
            "processing_time": int((time.time() - start_time) * 1000)
        }
    )
    
    # Cache common responses
    response_cache[cache_key] = {
        'response': result.dict(),
        'timestamp': time.time()
    }
    
    return result

@router.post("/voice-to-text")
async def voice_to_text(
    audio: UploadFile = File(...)
):
    # Simple placeholder - in production, integrate with speech service
    return {"data": {"text": "Voice input received", "confidence": 0.95}}

@router.post("/conversation/clear/{user_id}")
async def clear_conversation(user_id: str):
    # TODO: Clear context for user_id
    return {"message": f"Conversation context cleared for user {user_id}."}
