generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model AuthUser {
  id          Int      @id @default(autoincrement())
  email       String   @unique @db.VarChar(255)
  employeeId  String?  @unique @map("employee_id") @db.VarChar(50)
  firstName   String   @map("first_name") @db.VarChar(100)
  lastName    String   @map("last_name") @db.VarChar(100)
  isActive    Boolean  @default(true) @map("is_active")
  tenantId    String   @map("tenant_id") @db.VarChar(100)
  profilePic  String?  @map("profile_pic") @db.VarChar(255)
  lastLoginAt DateTime? @map("last_login_at")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  userRoles AuthUserRole[]
  sessions  AuthSession[]

  @@map("auth_users")
  @@index([email])
  @@index([employeeId])
  @@index([tenantId])
}

model AuthRole {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(50)
  tenantId    String   @map("tenant_id") @db.VarChar(100)
  permissions Json?
  description String?  @db.Text
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  userRoles AuthUserRole[]

  @@map("auth_roles")
  @@unique([name, tenantId], name: "unique_role_tenant")
  @@index([tenantId])
}

model AuthUserRole {
  id       Int    @id @default(autoincrement())
  userId   Int    @map("user_id")
  roleId   Int    @map("role_id")
  tenantId String @map("tenant_id") @db.VarChar(100)
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user AuthUser @relation(fields: [userId], references: [id], onDelete: Cascade)
  role AuthRole @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@map("auth_user_roles")
  @@unique([userId, roleId], name: "unique_user_role")
  @@index([tenantId])
}

model AuthSession {
  id        String   @id @db.VarChar(255)
  userId    Int      @map("user_id")
  tokenHash String   @map("token_hash") @db.VarChar(255)
  expiresAt DateTime @map("expires_at")
  isActive  Boolean  @default(true) @map("is_active")
  userAgent String?  @map("user_agent") @db.Text
  ipAddress String?  @map("ip_address") @db.VarChar(45)
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  user AuthUser @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("auth_sessions")
  @@index([userId])
  @@index([expiresAt])
  @@index([tokenHash])
}

model AuthTenant {
  id          String   @id @db.VarChar(100)
  name        String   @db.VarChar(255)
  domain      String   @unique @db.VarChar(255)
  isActive    Boolean  @default(true) @map("is_active")
  settings    Json?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("auth_tenants")
  @@index([domain])
}

model AuthAuditLog {
  id        Int      @id @default(autoincrement())
  userId    Int?     @map("user_id")
  tenantId  String   @map("tenant_id") @db.VarChar(100)
  action    String   @db.VarChar(100)
  resource  String?  @db.VarChar(255)
  details   Json?
  ipAddress String?  @map("ip_address") @db.VarChar(45)
  userAgent String?  @map("user_agent") @db.Text
  createdAt DateTime @default(now()) @map("created_at")

  @@map("auth_audit_logs")
  @@index([userId])
  @@index([tenantId])
  @@index([action])
  @@index([createdAt])
}
