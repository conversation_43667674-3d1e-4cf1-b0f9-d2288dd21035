import { Injectable, UnauthorizedException, Logger, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { randomBytes, createHash } from 'crypto';

import { GoogleOAuthStrategy } from './strategies/google-oauth.strategy';
import { UsersService } from '../users/users.service';
import { AuditService } from '../audit/audit.service';
import { PrismaService } from '../prisma/prisma.service';
import { 
  AuthResult, 
  JwtPayload, 
  RefreshTokenPayload, 
  SessionInfo 
} from './interfaces/auth.interface';
import { GoogleLoginDto, RefreshTokenDto, ValidateTokenDto } from './dto/login.dto';
import { TokenResponseDto, TokenValidationResponseDto } from './dto/token.dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private jwtService: JwtService,
    private configService: ConfigService,
    private googleStrategy: GoogleOAuthStrategy,
    private usersService: UsersService,
    private auditService: AuditService,
    private prisma: PrismaService,
  ) {}

  async loginWithGoogle(
    loginDto: GoogleLoginDto,
    userAgent?: string,
    ipAddress?: string,
  ): Promise<TokenResponseDto> {
    try {
      // Validate Google token and get user info
      const googleUser = await this.googleStrategy.getTokenInfo(loginDto.googleToken);
      
      // Get or create user
      let user = await this.usersService.findByEmail(googleUser.email);
      
      if (!user) {
        // Create new user
        user = await this.usersService.createFromGoogleUser(googleUser, loginDto.tenantId);
        
        await this.auditService.log({
          action: 'USER_CREATED',
          userId: user.id,
          tenantId: user.tenantId,
          details: { email: user.email, source: 'google_oauth' },
          ipAddress,
          userAgent,
        });
      } else if (!user.isActive) {
        throw new UnauthorizedException('User account is deactivated');
      }

      // Update last login
      await this.usersService.updateLastLogin(user.id);

      // Get user roles and permissions
      const userRoles = await this.usersService.getUserRoles(user.id);
      const permissions = await this.usersService.getUserPermissions(user.id);

      // Create session
      const sessionId = this.generateSessionId();
      const expiresAt = new Date();
      expiresAt.setSeconds(
        expiresAt.getSeconds() + 
        (loginDto.rememberMe ? 30 * 24 * 60 * 60 : parseInt(this.configService.get('JWT_EXPIRES_IN')))
      );

      // Generate tokens
      const tokens = await this.generateTokens(user, userRoles, permissions, sessionId, loginDto.rememberMe);

      // Store session
      await this.createSession({
        id: sessionId,
        userId: user.id,
        tokenHash: this.hashToken(tokens.accessToken),
        expiresAt,
        userAgent,
        ipAddress,
      });

      // Log successful login
      await this.auditService.log({
        action: 'LOGIN_SUCCESS',
        userId: user.id,
        tenantId: user.tenantId,
        details: { method: 'google_oauth', rememberMe: loginDto.rememberMe },
        ipAddress,
        userAgent,
      });

      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        tokenType: 'Bearer',
        expiresIn: tokens.expiresIn,
        user: {
          id: user.id,
          email: user.email,
          employeeId: user.employeeId,
          firstName: user.firstName,
          lastName: user.lastName,
          profilePic: user.profilePic,
          roles: userRoles,
          permissions,
          tenantId: user.tenantId,
        },
      };
    } catch (error) {
      this.logger.error('Google login failed:', error);
      
      if (error instanceof UnauthorizedException || error instanceof BadRequestException) {
        throw error;
      }
      
      throw new UnauthorizedException('Authentication failed');
    }
  }

  async refreshToken(refreshDto: RefreshTokenDto): Promise<TokenResponseDto> {
    try {
      // Verify refresh token
      const payload = this.jwtService.verify<RefreshTokenPayload>(refreshDto.refreshToken);
      
      if (payload.type !== 'refresh') {
        throw new UnauthorizedException('Invalid token type');
      }

      // Get user and session
      const user = await this.usersService.findById(payload.sub);
      if (!user || !user.isActive) {
        throw new UnauthorizedException('User not found or inactive');
      }

      const session = await this.findActiveSession(payload.sessionId);
      if (!session) {
        throw new UnauthorizedException('Session not found or expired');
      }

      // Get user roles and permissions
      const userRoles = await this.usersService.getUserRoles(user.id);
      const permissions = await this.usersService.getUserPermissions(user.id);

      // Generate new tokens
      const tokens = await this.generateTokens(user, userRoles, permissions, session.id);

      // Update session with new token hash
      await this.updateSessionToken(session.id, this.hashToken(tokens.accessToken));

      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        tokenType: 'Bearer',
        expiresIn: tokens.expiresIn,
        user: {
          id: user.id,
          email: user.email,
          employeeId: user.employeeId,
          firstName: user.firstName,
          lastName: user.lastName,
          profilePic: user.profilePic,
          roles: userRoles,
          permissions,
          tenantId: user.tenantId,
        },
      };
    } catch (error) {
      this.logger.error('Token refresh failed:', error);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(sessionId: string, userId: number): Promise<void> {
    try {
      // Deactivate session
      await this.deactivateSession(sessionId);

      // Log logout
      await this.auditService.log({
        action: 'LOGOUT',
        userId,
        details: { sessionId },
      });
    } catch (error) {
      this.logger.error('Logout failed:', error);
      throw error;
    }
  }

  async validateToken(validateDto: ValidateTokenDto): Promise<TokenValidationResponseDto> {
    try {
      // Verify token
      const payload = this.jwtService.verify<JwtPayload>(validateDto.token);
      
      // Get user
      const user = await this.usersService.findById(payload.sub);
      if (!user || !user.isActive) {
        return { valid: false, error: 'User not found or inactive' };
      }

      // Check session
      const session = await this.findActiveSession(payload.sessionId);
      if (!session) {
        return { valid: false, error: 'Session expired or invalid' };
      }

      // Check permissions if required
      let hasPermissions = true;
      if (validateDto.permissions && validateDto.permissions.length > 0) {
        hasPermissions = validateDto.permissions.every(permission =>
          payload.permissions.includes(permission)
        );
      }

      return {
        valid: true,
        hasPermissions,
        user: {
          id: user.id,
          email: user.email,
          employeeId: user.employeeId,
          firstName: user.firstName,
          lastName: user.lastName,
          profilePic: user.profilePic,
          roles: payload.roles,
          permissions: payload.permissions,
          tenantId: user.tenantId,
        },
      };
    } catch (error) {
      return { valid: false, error: 'Invalid token' };
    }
  }

  private async generateTokens(
    user: any,
    roles: string[],
    permissions: string[],
    sessionId: string,
    rememberMe = false,
  ) {
    const expiresIn = parseInt(this.configService.get('JWT_EXPIRES_IN'));
    const refreshExpiresIn = parseInt(this.configService.get('JWT_REFRESH_EXPIRES_IN'));

    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      employeeId: user.employeeId,
      firstName: user.firstName,
      lastName: user.lastName,
      tenantId: user.tenantId,
      roles,
      permissions,
      sessionId,
    };

    const refreshPayload: RefreshTokenPayload = {
      sub: user.id,
      sessionId,
      type: 'refresh',
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: rememberMe ? '30d' : `${expiresIn}s`,
    });

    const refreshToken = this.jwtService.sign(refreshPayload, {
      expiresIn: `${refreshExpiresIn}s`,
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: rememberMe ? 30 * 24 * 60 * 60 : expiresIn,
    };
  }

  private generateSessionId(): string {
    return randomBytes(32).toString('hex');
  }

  private hashToken(token: string): string {
    return createHash('sha256').update(token).digest('hex');
  }

  private async createSession(sessionData: {
    id: string;
    userId: number;
    tokenHash: string;
    expiresAt: Date;
    userAgent?: string;
    ipAddress?: string;
  }): Promise<void> {
    await this.prisma.authSession.create({
      data: sessionData,
    });
  }

  private async findActiveSession(sessionId: string): Promise<SessionInfo | null> {
    const session = await this.prisma.authSession.findFirst({
      where: {
        id: sessionId,
        isActive: true,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    return session;
  }

  private async updateSessionToken(sessionId: string, tokenHash: string): Promise<void> {
    await this.prisma.authSession.update({
      where: { id: sessionId },
      data: { tokenHash },
    });
  }

  private async deactivateSession(sessionId: string): Promise<void> {
    await this.prisma.authSession.update({
      where: { id: sessionId },
      data: { isActive: false },
    });
  }

  async cleanupExpiredSessions(): Promise<number> {
    const result = await this.prisma.authSession.deleteMany({
      where: {
        OR: [
          { expiresAt: { lt: new Date() } },
          { isActive: false },
        ],
      },
    });

    this.logger.log(`Cleaned up ${result.count} expired sessions`);
    return result.count;
  }
}
