import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../../users/users.service';
import { JwtPayload } from '../interfaces/auth.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private usersService: UsersService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
      issuer: 'tudip-auth-service',
      audience: 'tudip-applications',
    });
  }

  async validate(payload: JwtPayload) {
    try {
      // Verify user still exists and is active
      const user = await this.usersService.findById(payload.sub);
      
      if (!user) {
        throw new UnauthorizedException('User not found');
      }

      if (!user.isActive) {
        throw new UnauthorizedException('User account is deactivated');
      }

      // Verify session is still valid
      const session = await this.usersService.findActiveSession(payload.sessionId);
      
      if (!session) {
        throw new UnauthorizedException('Session expired or invalid');
      }

      if (session.expiresAt < new Date()) {
        throw new UnauthorizedException('Session expired');
      }

      // Return user info for request context
      return {
        id: user.id,
        email: user.email,
        employeeId: user.employeeId,
        firstName: user.firstName,
        lastName: user.lastName,
        tenantId: user.tenantId,
        roles: payload.roles,
        permissions: payload.permissions,
        sessionId: payload.sessionId,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Token validation failed');
    }
  }
}
