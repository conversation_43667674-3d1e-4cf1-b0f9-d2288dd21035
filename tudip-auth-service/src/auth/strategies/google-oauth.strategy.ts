import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OAuth2Client } from 'google-auth-library';
import { GoogleUserInfo } from '../interfaces/auth.interface';

@Injectable()
export class GoogleOAuthStrategy {
  private oauthClient: OAuth2Client;
  private allowedDomain: string;

  constructor(private configService: ConfigService) {
    const clientId = this.configService.get<string>('GOOGLE_CLIENT_ID');
    const clientSecret = this.configService.get<string>('GOOGLE_CLIENT_SECRET');
    this.allowedDomain = this.configService.get<string>('GOOGLE_WORKSPACE_DOMAIN') || 'tudip.com';
    
    this.oauthClient = new OAuth2Client(clientId, clientSecret);
  }

  async validateGoogleToken(token: string): Promise<GoogleUserInfo> {
    try {
      // Verify the token with Google
      const ticket = await this.oauthClient.verifyIdToken({
        idToken: token,
        audience: this.configService.get<string>('GOOGLE_CLIENT_ID'),
      });

      const payload = ticket.getPayload();
      
      if (!payload) {
        throw new UnauthorizedException('Invalid Google token payload');
      }

      // Check if email is verified
      if (!payload.email_verified) {
        throw new UnauthorizedException('Email not verified with Google');
      }

      // Check if email belongs to allowed domain
      if (payload.hd !== this.allowedDomain) {
        throw new UnauthorizedException(
          `Only ${this.allowedDomain} email addresses are allowed`
        );
      }

      return {
        id: payload.sub,
        email: payload.email,
        verified_email: payload.email_verified,
        name: payload.name,
        given_name: payload.given_name,
        family_name: payload.family_name,
        picture: payload.picture,
        locale: payload.locale,
        hd: payload.hd,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Failed to validate Google token');
    }
  }

  async getTokenInfo(accessToken: string): Promise<GoogleUserInfo> {
    try {
      // Get token info from Google
      const tokenInfo = await this.oauthClient.getTokenInfo(accessToken);
      
      if (!tokenInfo.email) {
        throw new UnauthorizedException('No email found in token');
      }

      // Extract domain from email
      const emailDomain = tokenInfo.email.split('@')[1];
      
      if (emailDomain !== this.allowedDomain) {
        throw new UnauthorizedException(
          `Only ${this.allowedDomain} email addresses are allowed`
        );
      }

      // Get user info from Google
      const userInfoResponse = await fetch(
        `https://www.googleapis.com/oauth2/v2/userinfo?access_token=${accessToken}`
      );

      if (!userInfoResponse.ok) {
        throw new UnauthorizedException('Failed to get user info from Google');
      }

      const userInfo = await userInfoResponse.json();

      return {
        id: userInfo.id,
        email: userInfo.email,
        verified_email: userInfo.verified_email,
        name: userInfo.name,
        given_name: userInfo.given_name,
        family_name: userInfo.family_name,
        picture: userInfo.picture,
        locale: userInfo.locale,
        hd: userInfo.hd,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Failed to validate Google access token');
    }
  }
}
