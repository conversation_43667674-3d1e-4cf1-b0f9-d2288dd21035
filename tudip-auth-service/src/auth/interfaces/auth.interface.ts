export interface JwtPayload {
  sub: number; // user id
  email: string;
  employeeId: string;
  firstName: string;
  lastName: string;
  tenantId: string;
  roles: string[];
  permissions: string[];
  sessionId: string;
  iat?: number;
  exp?: number;
  iss?: string;
  aud?: string;
}

export interface GoogleUserInfo {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  locale: string;
  hd?: string; // hosted domain
}

export interface AuthResult {
  user: {
    id: number;
    email: string;
    employeeId: string;
    firstName: string;
    lastName: string;
    profilePic?: string;
    tenantId: string;
    roles: string[];
    permissions: string[];
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
  session: {
    id: string;
    expiresAt: Date;
  };
}

export interface SessionInfo {
  id: string;
  userId: number;
  expiresAt: Date;
  isActive: boolean;
  userAgent?: string;
  ipAddress?: string;
  createdAt: Date;
}

export interface RefreshTokenPayload {
  sub: number; // user id
  sessionId: string;
  type: 'refresh';
  iat?: number;
  exp?: number;
}
