import { ApiProperty } from '@nestjs/swagger';

export class TokenResponseDto {
  @ApiProperty({
    description: 'JWT access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'Refresh token for getting new access tokens',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  refreshToken: string;

  @ApiProperty({
    description: 'Token type',
    example: 'Bearer',
  })
  tokenType: string;

  @ApiProperty({
    description: 'Token expiration time in seconds',
    example: 3600,
  })
  expiresIn: number;

  @ApiProperty({
    description: 'User information',
  })
  user: UserInfoDto;
}

export class UserInfoDto {
  @ApiProperty({
    description: 'User ID',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'User email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Employee ID',
    example: 'TUD001',
  })
  employeeId: string;

  @ApiProperty({
    description: 'First name',
    example: 'John',
  })
  firstName: string;

  @ApiProperty({
    description: 'Last name',
    example: 'Doe',
  })
  lastName: string;

  @ApiProperty({
    description: 'Profile picture URL',
    example: 'https://example.com/profile.jpg',
    required: false,
  })
  profilePic?: string;

  @ApiProperty({
    description: 'User roles',
    example: ['employee', 'developer'],
  })
  roles: string[];

  @ApiProperty({
    description: 'User permissions',
    example: ['read:documents', 'write:code'],
  })
  permissions: string[];

  @ApiProperty({
    description: 'Tenant ID',
    example: 'tudip',
  })
  tenantId: string;
}

export class TokenValidationResponseDto {
  @ApiProperty({
    description: 'Whether the token is valid',
    example: true,
  })
  valid: boolean;

  @ApiProperty({
    description: 'User information if token is valid',
    required: false,
  })
  user?: UserInfoDto;

  @ApiProperty({
    description: 'Error message if token is invalid',
    required: false,
  })
  error?: string;

  @ApiProperty({
    description: 'Whether user has required permissions',
    required: false,
  })
  hasPermissions?: boolean;
}
