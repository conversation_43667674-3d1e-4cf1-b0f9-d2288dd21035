import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsBoolean } from 'class-validator';

export class GoogleLoginDto {
  @ApiProperty({
    description: 'Google OAuth access token',
    example: '********************....',
  })
  @IsString()
  @IsNotEmpty()
  googleToken: string;

  @ApiProperty({
    description: 'Tenant ID for multi-tenant applications',
    example: 'tudip',
    required: false,
  })
  @IsString()
  @IsOptional()
  tenantId?: string;

  @ApiProperty({
    description: 'Remember me option for extended session',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  rememberMe?: boolean;
}

export class RefreshTokenDto {
  @ApiProperty({
    description: 'Refresh token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  @IsNotEmpty()
  refreshToken: string;
}

export class ValidateTokenDto {
  @ApiProperty({
    description: 'JWT token to validate',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    description: 'Required permissions to check',
    example: ['read:users', 'write:documents'],
    required: false,
  })
  @IsOptional()
  permissions?: string[];
}
