import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';

import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { RolesModule } from './roles/roles.module';
import { HealthModule } from './health/health.module';
import { AuditModule } from './audit/audit.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      useFactory: () => ({
        ttl: parseInt(process.env.RATE_LIMIT_TTL) || 60000,
        limit: parseInt(process.env.RATE_LIMIT_LIMIT) || 100,
      }),
    }),

    // Logging
    WinstonModule.forRootAsync({
      useFactory: () => ({
        level: process.env.LOG_LEVEL || 'info',
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.errors({ stack: true }),
          process.env.LOG_FORMAT === 'json'
            ? winston.format.json()
            : winston.format.simple(),
        ),
        transports: [
          new winston.transports.Console({
            handleExceptions: true,
            handleRejections: true,
          }),
          ...(process.env.NODE_ENV === 'production'
            ? [
                new winston.transports.File({
                  filename: 'logs/error.log',
                  level: 'error',
                }),
                new winston.transports.File({
                  filename: 'logs/combined.log',
                }),
              ]
            : []),
        ],
      }),
    }),

    // Core modules
    PrismaModule,
    AuthModule,
    UsersModule,
    RolesModule,
    HealthModule,
    AuditModule,
  ],
})
export class AppModule {}
