import { Injectable, NotFoundException, ConflictException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { GoogleUserInfo } from '../auth/interfaces/auth.interface';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {}

  async findById(id: number) {
    const user = await this.prisma.authUser.findUnique({
      where: { id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    return user;
  }

  async findByEmail(email: string) {
    const user = await this.prisma.authUser.findUnique({
      where: { email },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    return user;
  }

  async findByEmployeeId(employeeId: string) {
    const user = await this.prisma.authUser.findUnique({
      where: { employeeId },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    return user;
  }

  async createFromGoogleUser(googleUser: GoogleUserInfo, tenantId?: string) {
    const defaultTenantId = tenantId || this.configService.get('DEFAULT_TENANT_ID') || 'tudip';

    try {
      // Check if user already exists
      const existingUser = await this.findByEmail(googleUser.email);
      if (existingUser) {
        throw new ConflictException('User already exists');
      }

      // Create user
      const user = await this.prisma.authUser.create({
        data: {
          email: googleUser.email,
          firstName: googleUser.given_name,
          lastName: googleUser.family_name,
          profilePic: googleUser.picture,
          tenantId: defaultTenantId,
          isActive: true,
        },
      });

      // Assign default employee role
      await this.assignDefaultRole(user.id, defaultTenantId);

      this.logger.log(`Created new user: ${user.email}`);
      return user;
    } catch (error) {
      this.logger.error('Failed to create user from Google:', error);
      throw error;
    }
  }

  async updateLastLogin(userId: number) {
    await this.prisma.authUser.update({
      where: { id: userId },
      data: { lastLoginAt: new Date() },
    });
  }

  async getUserRoles(userId: number): Promise<string[]> {
    const userRoles = await this.prisma.authUserRole.findMany({
      where: { userId },
      include: {
        role: {
          where: { isActive: true },
        },
      },
    });

    return userRoles
      .filter(ur => ur.role)
      .map(ur => ur.role.name);
  }

  async getUserPermissions(userId: number): Promise<string[]> {
    const userRoles = await this.prisma.authUserRole.findMany({
      where: { userId },
      include: {
        role: {
          where: { isActive: true },
        },
      },
    });

    const permissions = new Set<string>();

    userRoles.forEach(ur => {
      if (ur.role && ur.role.permissions) {
        const rolePermissions = Array.isArray(ur.role.permissions) 
          ? ur.role.permissions 
          : [];
        rolePermissions.forEach(permission => permissions.add(permission));
      }
    });

    return Array.from(permissions);
  }

  async findActiveSession(sessionId: string) {
    const session = await this.prisma.authSession.findFirst({
      where: {
        id: sessionId,
        isActive: true,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    return session;
  }

  async updateProfile(userId: number, updateData: {
    firstName?: string;
    lastName?: string;
    profilePic?: string;
  }) {
    const user = await this.prisma.authUser.update({
      where: { id: userId },
      data: updateData,
    });

    return user;
  }

  async deactivateUser(userId: number) {
    // Deactivate user
    await this.prisma.authUser.update({
      where: { id: userId },
      data: { isActive: false },
    });

    // Deactivate all sessions
    await this.prisma.authSession.updateMany({
      where: { userId },
      data: { isActive: false },
    });

    this.logger.log(`Deactivated user: ${userId}`);
  }

  async activateUser(userId: number) {
    await this.prisma.authUser.update({
      where: { id: userId },
      data: { isActive: true },
    });

    this.logger.log(`Activated user: ${userId}`);
  }

  async assignRole(userId: number, roleId: number, tenantId: string) {
    // Check if role assignment already exists
    const existingAssignment = await this.prisma.authUserRole.findFirst({
      where: {
        userId,
        roleId,
      },
    });

    if (existingAssignment) {
      throw new ConflictException('Role already assigned to user');
    }

    const userRole = await this.prisma.authUserRole.create({
      data: {
        userId,
        roleId,
        tenantId,
      },
    });

    this.logger.log(`Assigned role ${roleId} to user ${userId}`);
    return userRole;
  }

  async removeRole(userId: number, roleId: number) {
    await this.prisma.authUserRole.deleteMany({
      where: {
        userId,
        roleId,
      },
    });

    this.logger.log(`Removed role ${roleId} from user ${userId}`);
  }

  private async assignDefaultRole(userId: number, tenantId: string) {
    // Find or create default employee role
    let employeeRole = await this.prisma.authRole.findFirst({
      where: {
        name: 'employee',
        tenantId,
      },
    });

    if (!employeeRole) {
      employeeRole = await this.prisma.authRole.create({
        data: {
          name: 'employee',
          tenantId,
          description: 'Default employee role',
          permissions: ['read:profile', 'update:profile'],
        },
      });
    }

    // Assign role to user
    await this.assignRole(userId, employeeRole.id, tenantId);
  }

  async getUserSessions(userId: number) {
    const sessions = await this.prisma.authSession.findMany({
      where: {
        userId,
        isActive: true,
        expiresAt: {
          gt: new Date(),
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return sessions;
  }

  async revokeSession(sessionId: string, userId: number) {
    await this.prisma.authSession.updateMany({
      where: {
        id: sessionId,
        userId,
      },
      data: {
        isActive: false,
      },
    });

    this.logger.log(`Revoked session ${sessionId} for user ${userId}`);
  }

  async revokeAllSessions(userId: number) {
    await this.prisma.authSession.updateMany({
      where: { userId },
      data: { isActive: false },
    });

    this.logger.log(`Revoked all sessions for user ${userId}`);
  }
}
