{"name": "@tudip/auth-service", "version": "1.0.0", "description": "Reusable authentication service for Tudip applications", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "docker:build": "docker build -t tudip/auth-service .", "docker:run": "docker-compose up -d"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/jwt": "^10.1.0", "@nestjs/passport": "^10.0.0", "@nestjs/swagger": "^7.1.0", "@nestjs/throttler": "^4.2.1", "@prisma/client": "^5.0.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-google-oauth20": "^2.0.0", "google-auth-library": "^9.0.0", "jsonwebtoken": "^9.0.2", "bcrypt": "^5.1.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "helmet": "^7.0.0", "cors": "^2.8.5", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "winston": "^3.10.0", "nest-winston": "^1.9.4", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/passport-jwt": "^3.0.9", "@types/passport-google-oauth20": "^2.0.11", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^5.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "keywords": ["authentication", "o<PERSON>h", "jwt", "<PERSON><PERSON><PERSON>", "tudip", "microservice"], "author": "Tudip Technologies", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tudip/auth-service.git"}}