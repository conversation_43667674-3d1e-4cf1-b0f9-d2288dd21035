# Database Configuration
DATABASE_URL="mysql://username:password@localhost:3306/tudip_auth"

# Google OAuth Configuration
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_WORKSPACE_DOMAIN="tudip.com"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="3600"  # 1 hour in seconds
JWT_REFRESH_EXPIRES_IN="604800"  # 7 days in seconds

# Service Configuration
PORT=3000
NODE_ENV="development"
API_PREFIX="api/v1"

# CORS Configuration
CORS_ORIGINS="http://localhost:3000,http://localhost:3001,https://yourapp.com"
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_TTL=60000  # 1 minute
RATE_LIMIT_LIMIT=100  # requests per TTL

# Tenant Configuration
DEFAULT_TENANT_ID="tudip"
MULTI_TENANT_ENABLED=true

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-change-this"

# Logging
LOG_LEVEL="info"
LOG_FORMAT="json"

# Health Check
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH="/health"

# Swagger Documentation
SWAGGER_ENABLED=true
SWAGGER_PATH="/docs"
SWAGGER_TITLE="Tudip Authentication Service"
SWAGGER_DESCRIPTION="Reusable authentication service for Tudip applications"
SWAGGER_VERSION="1.0.0"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER="<EMAIL>"
SMTP_PASS="your-email-password"

# Redis Configuration (for session storage - optional)
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""
REDIS_DB=0

# Monitoring
METRICS_ENABLED=true
METRICS_PATH="/metrics"

# Audit Logging
AUDIT_LOG_ENABLED=true
AUDIT_LOG_RETENTION_DAYS=90
